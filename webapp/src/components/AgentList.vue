<template>
  <div class="agent-list">
    <div class="overflow-x-auto">
      <div v-if="agents.length === 0" class="text-center py-8">
        <p class="text-gray-500 text-lg">No agents available</p>
        <div class="flex justify-center space-x-4 mt-4">
          <button
            @click="router.push('/agents/create')"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            Create New Agent
          </button>
        </div>
      </div>
      <table v-else class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Agent ID</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Organization</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Address</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">AI Score</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completed Cases</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Skills</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="agent in agents" :key="agent.id">
            <td class="px-6 py-4 whitespace-nowrap">{{ agent.id }}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              <router-link :to="{ name: 'agent-details', params: { id: agent.id } }" class="text-indigo-600 hover:text-indigo-900">
                {{ agent.name }}
              </router-link>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">{{ agent.organization?.name || 'No Organization' }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ agent.address }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ agent.aiClassifiedScore ? agent.aiClassifiedScore.toFixed(2) : '0.00' }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ getCompletedCases(agent) }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ agent.completedSkills?.join(', ') || 'None' }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <button
                @click="viewAgentDetails(agent.id)"
                class="text-indigo-600 hover:text-indigo-900 mr-4"
              >
                View
              </button>
              <button
                @click="deleteAgent(agent.id)"
                class="text-red-600 hover:text-red-900"
              >
                <svg
                  class="w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                    clip-rule="evenodd"
                  />
                </svg>
              </button>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Pagination for Agents -->
      <Pagination
        v-if="agents.length > 0"
        :current-page="paginationState.currentPage"
        :total-pages="paginationState.totalPages"
        :total-items="paginationState.totalItems"
        :page-size="paginationState.pageSize"
        @page-change="handlePageChange($event)"
        @page-size-change="handlePageSizeChange($event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, defineProps, defineEmits } from 'vue';
import { useRouter } from 'vue-router';
import axios from 'axios';
import Pagination from './utility/Pagination.vue';

const props = defineProps<{ showAutoAssignModal: boolean }>();
const emit = defineEmits(['close-auto-assign-modal']);

const router = useRouter();
const agents = ref<any[]>([]);

const paginationState = reactive({
  currentPage: 1,
  totalPages: 1,
  totalItems: 0,
  pageSize: 10,
});

const fetchAgents = async () => {
  try {
    // Use proxy and filter for role 'agent'
    const response = await axios.get('/http://localhost:3000/api/agents/search', {
      params: {
        role: 'agent', // Only fetch agents with role 'agent'
        page: paginationState.currentPage,
        limit: paginationState.pageSize
      }
    });
    agents.value = response.data.data;
    paginationState.totalItems = response.data.meta.total;
    paginationState.totalPages = response.data.meta.totalPages;
    paginationState.currentPage = response.data.meta.page;
  } catch (error) {
    console.error('Error fetching agents:', error);
  }
};

const deleteAgent = async (agentId: number) => {
  const confirmed = confirm(`Are you sure you want to delete agent ${agentId}? This action cannot be undone.`);

  if (!confirmed) {
    return;
  }

  try {
    await axios.delete(`/http://localhost:3000/api/agents/${agentId}`);
    alert('Agent deleted successfully');
    fetchAgents(); // Refresh the list after deletion
  } catch (error: any) {
    console.error('Error deleting agent:', error);
    alert('Failed to delete agent. Please try again.');
  }
};

const getCompletedCases = (agent: any) => {
  return agent.caseConnections?.filter(
    (conn: any) => conn.status === 'COMPLETED'
  ).length || 0;
};

const viewAgentDetails = (agentId: number) => {
  router.push(`/agents/${agentId}`);
};

const handlePageChange = (page: number) => {
  paginationState.currentPage = page;
  fetchAgents();
};

const handlePageSizeChange = (pageSize: number) => {
  paginationState.pageSize = pageSize;
  paginationState.currentPage = 1;
  fetchAgents();
};

onMounted(() => {
  fetchAgents();
});
</script>

<style scoped>
.agent-list {
  padding: 1rem;
}
</style>
