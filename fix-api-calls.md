# API Calls Fix Summary

## Files that need fixing:

1. **AgentList.vue** - Lines 181, 204, 231, 245, 295
2. **AgentCreate.vue** - Line 130
3. **AgentDetail.vue** - Line 240
4. **CaseDetails.vue** - Lines 889, 1783, 1789
5. **OcrImport.vue** - Line 191
6. **PrescriberDetails.vue** - Line 294
7. **PrescriberSearch.vue** - Line 97

## Changes needed:
- Replace `http://localhost:3000/api/` with `/api/`
- Replace `http://localhost:3000/` with `/api/` (for endpoints without /api prefix)

## Quick Fix Strategy:
1. Fix the most critical files first (AgentList, CaseDetails, Dashboard)
2. Update Vite proxy config if needed
3. Test organization selector functionality
